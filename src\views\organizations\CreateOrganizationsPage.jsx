import { useDisclosure } from "@heroui/react";
import { useState } from "react";
import { Form, useForm } from "react-hook-form";
import PageWrapper from "../../components/layout/PageWrapper";

// Import components
import ActionButtons from "./components/ActionButtons";
import CreateAdminModal from "./components/CreateAdminModal";
import OrganizationInfoForm from "./components/OrganizationInfoForm";
import PermissionsSection from "./components/PermissionsSection";
import RoleSection from "./components/RoleSection";
import UserSearchSection from "./components/UserSearchSection";

const CreateOrganizationsPage = () => {
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const MAX_SELECTED_USERS = 1;

  // Disclosure for create modal
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

  const { control, handleSubmit } = useForm({
    defaultValues: {},
  });

  const handleUsersChange = (users) => {
    setSelectedUsers(users);
  };

  const handlePermissionsChange = (permissions) => {
    setSelectedPermissions(permissions);
  };

  const onSubmit = (data) => {
    const permissionsString = selectedPermissions.join(",");

    const formData = {
      ...data,
      entity_id:
        MAX_SELECTED_USERS === 1
          ? selectedUsers[0]?.id
          : selectedUsers.map((user) => user.id),
      permissions: permissionsString,
    };
    console.log(formData);
  };

  return (
    <PageWrapper hasTitle={false}>
      <Form
        className="flex flex-col gap-4"
        control={control}
        onSubmit={handleSubmit(onSubmit)}
      >
        <OrganizationInfoForm control={control} />

        <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <UserSearchSection
            onUsersChange={handleUsersChange}
            onOpenCreateModal={onOpen}
            MAX_SELECTED_USERS={MAX_SELECTED_USERS}
          />

          <CreateAdminModal
            isOpen={isOpen}
            onOpenChange={onOpenChange}
            onClose={onClose}
          />

          <RoleSection control={control} />

          <PermissionsSection onPermissionsChange={handlePermissionsChange} />

          <ActionButtons
            onCancel={() => {}}
            onSubmit={handleSubmit(onSubmit)}
          />
        </div>
      </Form>
    </PageWrapper>
  );
};

export default CreateOrganizationsPage;
