import { Checkbox, addToast } from "@heroui/react";
import PropTypes from "prop-types";
import { Fragment, useEffect, useMemo, useState } from "react";
import api from "../../../api";

const PermissionsSection = ({ onPermissionsChange }) => {
  const [selectedPermissions, setSelectedPermissions] = useState([]);

  // Get Permissions Data
  const { data: _permissions, error: permissionsError } =
    api.Permissions.list.useQuery();
  const permissions = useMemo(() => _permissions?.data, [_permissions]);

  useEffect(() => {
    if (_permissions?.status === false || permissionsError) {
      addToast({
        title: _permissions?.message
          ? _permissions?.message
          : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [permissionsError, _permissions]);

  const handleSelectAll = (groupName, groupPermissions) => {
    const allPermissionIds = groupPermissions.map((perm) => perm.id);
    const isAllSelected = allPermissionIds.every((permId) =>
      selectedPermissions.includes(permId),
    );

    let newSelectedPermissions;
    if (isAllSelected) {
      newSelectedPermissions = selectedPermissions.filter(
        (perm) => !allPermissionIds.includes(perm),
      );
    } else {
      const filtered = selectedPermissions.filter(
        (perm) => !allPermissionIds.includes(perm),
      );
      newSelectedPermissions = [...filtered, ...allPermissionIds];
    }

    setSelectedPermissions(newSelectedPermissions);
    onPermissionsChange?.(newSelectedPermissions);
  };

  const handleSinglePermission = (permissionId, groupPermissions) => {
    const allPermissionIds = groupPermissions.map((perm) => perm.id);

    const isCurrentlySelected = selectedPermissions.includes(permissionId);

    const filteredPermissions = selectedPermissions.filter(
      (perm) => !allPermissionIds.includes(perm),
    );

    let newSelectedPermissions;
    if (isCurrentlySelected) {
      newSelectedPermissions = filteredPermissions;
    } else {
      newSelectedPermissions = [...filteredPermissions, permissionId];
    }

    setSelectedPermissions(newSelectedPermissions);
    onPermissionsChange?.(newSelectedPermissions);
  };

  const isSelectAllChecked = (groupPermissions) => {
    const allPermissionIds = groupPermissions.map((perm) => perm.id);
    return allPermissionIds.every((permId) =>
      selectedPermissions.includes(permId),
    );
  };

  const isPermissionChecked = (permissionId) => {
    return selectedPermissions.includes(permissionId);
  };

  const handleFullAccess = () => {
    if (!permissions) return;

    const allPermissions = permissions.flatMap((group) =>
      group.permissions.map((perm) => perm.id),
    );

    const areAllSelected = allPermissions.every((permId) =>
      selectedPermissions.includes(permId),
    );

    let newSelectedPermissions;
    if (areAllSelected) {
      newSelectedPermissions = [];
    } else {
      newSelectedPermissions = allPermissions;
    }

    setSelectedPermissions(newSelectedPermissions);
    onPermissionsChange?.(newSelectedPermissions);
  };

  const isFullAccessChecked = () => {
    if (!permissions) return false;

    const allPermissions = permissions.flatMap((group) =>
      group.permissions.map((perm) => perm.id),
    );

    return (
      allPermissions.length > 0 &&
      allPermissions.every((permId) => selectedPermissions.includes(permId))
    );
  };
  return (
    <div className="flex flex-col gap-4">
      <p className="font-medium mt-2">دسترسی‌ها</p>

      <Checkbox
        value={"all"}
        key={"all"}
        size="lg"
        radius="full"
        color="primary"
        isSelected={isFullAccessChecked()}
        onChange={handleFullAccess}
        classNames={{
          label: "text-sm font-medium text-inherit",
          base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
        }}
      >
        دسترسی کامل
      </Checkbox>

      {permissions?.map((item) => {
        return (
          <Fragment key={item?.name}>
            <p className="font-medium mt-2">{item?.display_name}</p>
            <div className="flex items-center gap-3">
              <Checkbox
                value={item?.name}
                key={item?.name}
                size="lg"
                radius="full"
                color="primary"
                isSelected={isSelectAllChecked(item?.permissions)}
                onChange={() => handleSelectAll(item?.name, item?.permissions)}
                classNames={{
                  label: "text-sm font-medium text-inherit",
                  base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                }}
              >
                انتخاب همه
              </Checkbox>

              {item?.permissions?.map((perm) => {
                return (
                  <Checkbox
                    value={perm?.id}
                    key={perm?.id}
                    size="lg"
                    radius="full"
                    color="primary"
                    isSelected={isPermissionChecked(perm?.id)}
                    onChange={() =>
                      handleSinglePermission(perm?.id, item?.permissions)
                    }
                    classNames={{
                      label: "text-sm font-medium text-inherit",
                      base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                    }}
                  >
                    {perm?.display_name}
                  </Checkbox>
                );
              })}
            </div>
          </Fragment>
        );
      })}
    </div>
  );
};

PermissionsSection.propTypes = {
  onPermissionsChange: PropTypes.func,
};

export default PermissionsSection;
