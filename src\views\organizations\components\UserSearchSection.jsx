import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ow,
  <PERSON><PERSON><PERSON>,
  Spinner,
  addToast,
} from "@heroui/react";
import { AnimatePresence, motion } from "framer-motion";
import { Add, Trash } from "iconsax-reactjs";
import { parseAsString, useQueryStates } from "nuqs";
import PropTypes from "prop-types";
import { useEffect, useMemo, useState } from "react";
import api from "../../../api";
import SearchFilter from "../../../components/table/filters/SearchFilter";

const UserSearchSection = ({
  onUsersChange,
  onOpenCreateModal,
  MAX_SELECTED_USERS = 1,
}) => {
  const [searchFocused, setSearchFocused] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);

  const [usernameQuery] = useQueryStates({
    username: parseAsString,
  });

  // Get Entities Data
  const {
    data: _data,
    isLoading,
    error,
  } = api.Entities.list.useQuery({
    enabled: !!usernameQuery.username,
    variables: {
      query: {
        username: usernameQuery?.username,
      },
    },
  });
  const users = useMemo(() => _data?.data, [_data]);

  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  const handleSelectUser = (user) => {
    if (MAX_SELECTED_USERS === 1) {
      const newSelectedUsers = [user];
      setSelectedUsers(newSelectedUsers);
      onUsersChange?.(newSelectedUsers);
    } else {
      setSelectedUsers((prev) => {
        const isAlreadySelected = prev.some((u) => u.id === user.id);
        let newSelectedUsers;
        if (isAlreadySelected) {
          newSelectedUsers = prev.filter((u) => u.id !== user.id);
        } else if (prev.length >= MAX_SELECTED_USERS) {
          newSelectedUsers = prev;
        } else {
          newSelectedUsers = [...prev, user];
        }
        onUsersChange?.(newSelectedUsers);
        return newSelectedUsers;
      });
    }
  };

  const handleRemoveUser = (userId) => {
    const newSelectedUsers = selectedUsers.filter((user) => user.id !== userId);
    setSelectedUsers(newSelectedUsers);
    onUsersChange?.(newSelectedUsers);
  };

  return (
    <>
      <div className="flex w-full md:flex-nowrap flex-wrap items-center gap-4">
        <div className="relative w-full">
          <SearchFilter
            name="username"
            radius="full"
            placeholder="جستجوی بر اساس نام یا شماره موبایل"
            classNames={{
              base: "w-full",
              inputWrapper:
                "!bg-background hover:!bg-background-100 min-h-11 shadow-sm border border-foreground-100 hover:border-foreground-200 transition-colors",
            }}
            onFocus={() => setSearchFocused(true)}
            onBlur={() => setTimeout(() => setSearchFocused(false), 200)}
            endContent={isLoading && <Spinner size="sm" />}
          />
          <AnimatePresence>
            {searchFocused && (
              <motion.div
                initial={{ opacity: 0, y: -20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.95 }}
                transition={{ duration: 0.15, ease: "easeOut" }}
                className="absolute border border-foreground-100 flex items-center justify-center flex-col rounded-2xl p-3 shadow-lg bg-background mt-1 w-full max-h-64 z-10"
              >
                <ScrollShadow hideScrollBar className="h-full w-full">
                  <ul className="space-y-2">
                    {!isLoading && !users?.data && (
                      <p className="text-center text-sm">نتیجه‌ای یافت نشد</p>
                    )}
                    {isLoading &&
                      !users?.data &&
                      [1, 2, 3].map((index) => (
                        <motion.li
                          key={index}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.1 }}
                          className="cursor-pointer hover:bg-foreground-100/50 rounded-lg px-2 py-1.5"
                        >
                          <div className="flex justify-between items-center">
                            <div className="flex gap-2 items-center">
                              <Skeleton className="flex-shrink-0 rounded-full size-10" />
                              <div className="flex flex-col gap-1">
                                <Skeleton className="h-3 w-24 rounded-lg" />
                                <Skeleton className="h-3 w-16 rounded-lg" />
                              </div>
                            </div>
                            <Skeleton className="h-8 w-16 rounded-full" />
                          </div>
                        </motion.li>
                      ))}
                    {users &&
                      !isLoading &&
                      users?.data?.map((item) => (
                        <motion.li
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.1 }}
                          key={item.id}
                          onClick={() => handleSelectUser(item)}
                          className="cursor-pointer hover:bg-foreground-100/50 rounded-lg px-2 py-1.5"
                        >
                          <div className="flex justify-between items-center">
                            <div className="flex gap-2 items-center">
                              <Avatar
                                alt={item.fullname}
                                className="flex-shrink-0"
                                src={item.avatar}
                              />
                              <div className="flex flex-col">
                                <span className="text-small ">
                                  {item.fullname}
                                </span>
                                <span className="text-tiny text-default-400">
                                  {item.mobile}
                                </span>
                              </div>
                            </div>
                          </div>
                        </motion.li>
                      ))}
                  </ul>
                </ScrollShadow>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <Button
          type="button"
          color="primary"
          radius="full"
          className="px-6 shrink-0"
          onPress={onOpenCreateModal}
          startContent={<Add className="size-6" />}
        >
          ایجاد ادمین جدید
        </Button>
      </div>

      {selectedUsers.length > 0 && (
        <ul className="space-y-4">
          {selectedUsers.map((user) => (
            <li
              key={user.id}
              className="flex items-center rounded-3xl gap-4 p-6 border border-foreground-100 shadow-sm"
            >
              <Avatar
                alt={user.fullname}
                className="flex-shrink-0"
                src={user.avatar}
                size="lg"
              />

              <div className="flex items-center font-medium gap-2">
                <p className="text-foreground-400">نام و نام خانوادگی: </p>
                <p>{user.fullname}</p>
              </div>

              <div className="flex items-center font-medium gap-2">
                <p className="text-foreground-400">شماره تماس: </p>
                <p>{user.mobile}</p>
              </div>

              <Button
                className="ms-auto"
                isIconOnly
                radius="full"
                color="danger"
                variant="light"
                onPress={() => handleRemoveUser(user.id)}
              >
                <Trash />
              </Button>
            </li>
          ))}
        </ul>
      )}
    </>
  );
};

UserSearchSection.propTypes = {
  onUsersChange: PropTypes.func,
  onOpenCreateModal: PropTypes.func,
  MAX_SELECTED_USERS: PropTypes.number,
};

export default UserSearchSection;
